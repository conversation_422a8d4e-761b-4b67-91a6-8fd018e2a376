import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_trading/core/extension/ext.dart';
import 'package:vp_trading/cubit/derivative/validate_order/derivative_validate_order_cubit.dart';
import 'package:vp_trading/cubit/place_order/available_trade/available_trade_cubit.dart';
import 'package:vp_trading/model/order/order_book_model.dart';
import 'package:vp_trading/utils/text_inputformater.dart';

import 'derivatives_order_input_field.dart';
import 'dialog_edit_derivatives_order.dart';

/// Widget chứa các input fields (quantity và price) với validation
class DerivativesOrderInputSection extends StatelessWidget {
  final TextEditingController quantityController;
  final TextEditingController priceController;
  final FocusNode quantityFocusNode;
  final FocusNode priceFocusNode;
  final Function(int) onQuantityUpdate;
  final Function(double) onPriceUpdate;
  final OrderBookModel item;
  final int quantity;
  final double price;
  final EditMode editMode;

  const DerivativesOrderInputSection({
    super.key,
    required this.quantityController,
    required this.priceController,
    required this.quantityFocusNode,
    required this.priceFocusNode,
    required this.onQuantityUpdate,
    required this.onPriceUpdate,
    required this.item,
    required this.quantity,
    required this.price,
    required this.editMode,
  });

  @override
  Widget build(BuildContext context) {
    final validateOrderCubit = context.read<DerivativeValidateOrderCubit>();
    var stateAvailableTradeCubit = context.watch<AvailableTradeCubit>();
    return Column(
      children: [
        // Quantity input with +/- buttons
        BlocBuilder<DerivativeValidateOrderCubit, DerivativeValidateOrderState>(
          builder: (context, state) {
            final isQuantityEnabled = editMode == EditMode.quantity;
            return DerivativesOrderInputField(
              controller: quantityController,
              label: 'Số lượng',
              focusNode: quantityFocusNode,
              enabled: isQuantityEnabled,
              onDecrease:
                  isQuantityEnabled
                      ? () => onQuantityUpdate(quantity - 1)
                      : null,
              onIncrease:
                  isQuantityEnabled
                      ? () => onQuantityUpdate(quantity + 1)
                      : null,
              onChanged:
                  isQuantityEnabled
                      ? (value) {
                        final newValue = int.tryParse(value);
                        if (newValue != null && newValue >= 0) {
                          onQuantityUpdate(newValue);
                        }
                        validateOrderCubit.onChangeVolume(value);
                      }
                      : null,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
                ...volumeInputFormatter,
              ],
              keyboardType: TextInputType.number,
              errorText:
                  stateAvailableTradeCubit.state.status ==
                          AvailableTradeStatus.loading
                      ? null
                      : state.errorVolume.isEditOrderError
                      ? state.errorVolume.message(
                        validateOrderCubit.maxVolume().toString(),
                      )
                      : null,
            );
          },
        ),
        const SizedBox(height: 8),

        // Price input with +/- buttons
        BlocConsumer<
          DerivativeValidateOrderCubit,
          DerivativeValidateOrderState
        >(
          bloc: validateOrderCubit,
          listenWhen:
              (previous, current) =>
                  previous.currentPrice != current.currentPrice,
          listener: (context, state) {
            if (state.currentPrice != null) {
              priceController.text = state.currentPrice!;
              priceController.selection = TextSelection.fromPosition(
                TextPosition(offset: priceController.text.length),
              );
              // Update the parent widget with the new price value
              final newPrice = state.currentPrice!.priceDerivative ?? 0.0;
              onPriceUpdate(newPrice);
            }
          },
          builder: (context, state) {
            final isPriceEnabled = editMode == EditMode.price;
            return DerivativesOrderInputField(
              controller: priceController,
              label: 'Giá đặt',
              focusNode: priceFocusNode,
              enabled: isPriceEnabled,
              onDecrease:
                  isPriceEnabled
                      ? () {
                        // Use proper price step logic for decrease
                        final currentText = priceController.text;
                        validateOrderCubit.priceTap(
                          text: currentText.isEmpty ? '0' : currentText,
                          increase: false,
                        );
                      }
                      : null,
              onIncrease:
                  isPriceEnabled
                      ? () {
                        // Use proper price step logic for increase
                        final currentText = priceController.text;
                        validateOrderCubit.priceTap(
                          text: currentText.isEmpty ? '0' : currentText,
                          increase: true,
                        );
                      }
                      : null,
              onChanged:
                  isPriceEnabled
                      ? (value) {
                        // Use validateOrderCubit for proper price validation and formatting
                        validateOrderCubit.onChangePrice(value);
                      }
                      : null,
              inputFormatters: [
                removeZeroStartInputFormatter,
                ...priceInputFormatter,
              ],
              keyboardType: const TextInputType.numberWithOptions(
                decimal: true,
              ),
              errorText:
                  state.errorPrice.isErrorEditOrder
                      ? state.errorPrice.message
                      : null,
            );
          },
        ),
      ],
    );
  }
}
